#include "board.h"
#include "stdio.h"
#include "motor.h"
#include "Emm_V5.h"
#include "stdlib.h"  // 包含abs函数

/*
* 主函数：
* 云台控制功能 - 根据相机坐标和中心点坐标实现插补运动控制
* 让云台控制摄像头中心到达相机传回的目标坐标位置
*/

// 摄像头中心点坐标定义（可根据实际摄像头分辨率调整）
#define CAMERA_CENTER_X  320   // 摄像头中心X坐标（假设640x480分辨率）
#define CAMERA_CENTER_Y  240   // 摄像头中心Y坐标

// 云台控制参数
#define MIN_ERROR_THRESHOLD 5      // 最小误差阈值（像素）
#define MAX_STEP_LIMIT     1000    // 单次最大步数限制

// 声明外部变量（在motor.c中定义）
extern int16_t actual_point[30][2];  // 实际坐标点数组（与motor.h中定义保持一致）

int main(void)
{
	// 系统初始化
	board_init();

	// 初始化步进计数器
	Usartdata.x_step = 0;
	Usartdata.y_step = 0;

	printf("Gimbal Control System Ready (Serial Control Mode)\r\n");
	printf("Camera Center: (%d, %d)\r\n", CAMERA_CENTER_X, CAMERA_CENTER_Y);
	printf("Using vImage_To_Actual for precise coordinate conversion\r\n");
	vLED_Blink(2);  // LED闪烁2次表示系统就绪

	for(;;)
	{
		// 检查是否有新的串口3数据并解析
		vData_Get();  // 这个函数会自动检查、解析并打印接收到的数据

		// 如果接收到相机数据，处理云台控制
		if(Usartdata.x != 0 || Usartdata.y != 0) {
			// 计算目标坐标与摄像头中心的误差
			int16_t error_x = Usartdata.x - CAMERA_CENTER_X;
			int16_t error_y = Usartdata.y - CAMERA_CENTER_Y;

			printf("Target: (%d, %d), Center: (%d, %d), Error: (%d, %d)\r\n",
			       Usartdata.x, Usartdata.y, CAMERA_CENTER_X, CAMERA_CENTER_Y, error_x, error_y);

			// 检查误差是否超过阈值
			if(abs(error_x) > MIN_ERROR_THRESHOLD || abs(error_y) > MIN_ERROR_THRESHOLD) {
				// 使用vImage_To_Actual函数进行精确的像素到步数转换
				int16_t error_array[1][2] = {{error_x, error_y}};
				vImage_To_Actual(error_array, 1);

				// 获取转换后的精确步数（从actual_point数组中）
				int16_t move_x = actual_point[0][0];
				int16_t move_y = actual_point[0][1];

				printf("Precise conversion: Error(%d,%d) -> Steps(%d,%d)\r\n",
				       error_x, error_y, move_x, move_y);

				// 限制单次移动的最大步数（安全保护）
				if(move_x > MAX_STEP_LIMIT) move_x = MAX_STEP_LIMIT;
				if(move_x < -MAX_STEP_LIMIT) move_x = -MAX_STEP_LIMIT;
				if(move_y > MAX_STEP_LIMIT) move_y = MAX_STEP_LIMIT;
				if(move_y < -MAX_STEP_LIMIT) move_y = -MAX_STEP_LIMIT;

				printf("Final gimbal movement: X=%d steps, Y=%d steps\r\n", move_x, move_y);

				// 执行云台运动（相对运动，使用串口控制模式）
				int16_t target_x = Usartdata.x_step + move_x;
				int16_t target_y = Usartdata.y_step + move_y;

				printf("Moving to absolute position: (%d, %d)\r\n", target_x, target_y);
				vInterpolation_Point_Serial(target_x, target_y);  // 串口控制模式

				// LED闪烁指示运动完成
				vLED_Blink(1);
			} else {
				printf("Target within threshold, no movement needed\r\n");
			}

			// 清除数据
			Usartdata.x = 0;
			Usartdata.y = 0;
		}
	}
}
