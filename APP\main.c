#include "board.h"
#include "stdio.h"
#include "motor.h"
#include "Emm_V5.h"

/*
* 主函数：
* 支持串口3接收相机数据并处理电机运动
*/

int main(void)
{
	// 系统初始化
	board_init();

	// 初始化步进计数器
	Usartdata.x_step = 0;
	Usartdata.y_step = 0;

	vLED_Blink(2);  // LED闪烁2次表示系统就绪

	for(;;)
	{
		// 检查是否有新的串口3数据并解析
		vData_Get();  // 这个函数会自动检查、解析并打印接收到的数据

		// 如果接收到相机数据，处理电机运动
		if(Usartdata.x != 0 || Usartdata.y != 0) {
			printf("Camera data received: (%d, %d), executing movement...\r\n",
			       Usartdata.x, Usartdata.y);
			vInterpolation_Point_Serial(Usartdata.x, Usartdata.y);
			Usartdata.x = 0;
			Usartdata.y = 0;
		}
	}
}
